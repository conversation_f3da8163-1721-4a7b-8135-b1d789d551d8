import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@core/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";
import { AppSidebar } from "@core/components/app-sidebar";

const storyId = 5

let arrayTwo = [
      {
        name: 'First',
        id: 1,
        libraries: {
          nodes: [
            {storyId: 1},
            {storyId: 3},
            {storyId: 5},
          ]
        }
      },
      {
        name: 'Second',
        id: 2,
        libraries: {
          nodes: [
            {storyId: 4},
            {storyId: 5},
            {storyId: 6},
          ]
        }
      },
      {
        name: 'Third',
        id: 3,
        libraries: {
          nodes: [
            {storyId: 3},
            {storyId: 2},
            {storyId: 1},
          ]
        }
      }
    ]

export default function Dashboard({
  behavioural_skills,
  technical_skills,
  tech_data_user,
}) {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [userData, setUserData] = useState(null);

  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if no session
        router.push("/");
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if session is lost
        router.push("/");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Fetch user profile when session is available
  useEffect(() => {
    if (session) {
      getUserProfile();
    }
  }, [session]);

  /* --- DEBUG --- */

  console.log("tech_data_user");
  console.log(tech_data_user);

  console.log("userData");
  console.log(userData && userData.role);

  // const filteredTechData =
  //   userData && tech_data_user
  //     ? tech_data_user.filter(
  //         (item) => item.role_mapping && item.role_mapping.id === userData.role
  //       )
  //     : [];

  // maybe I need to map over it ???
  // map over each and filter!!


arrayTwo.filter(el => el.libraries.nodes.filter(n => n.storyId === storyId).length > 0)))


  // const filteredTechData = tech_data_user.filter(
  //   (el) =>
  //     el.role_mapping.map((n) => n.role_id === (userData && userData.role))
  //       .length > 0
  // );

  // console.log("filteredTechData");
  // console.log(filteredTechData);

  // console.log("behavioural_skills");
  // console.log(behavioural_skills);

  // console.log("technical_skills");
  // console.log(technical_skills);

  // filter the array based on user role

  /* --- DEBUG --- */

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render dashboard if no session (will redirect)
  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar
        userData={userData}
        behavioural_skills={behavioural_skills}
        technical_skills={technical_skills}
      />

      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-3">
            <SidebarTrigger />

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink>My Skills Snapshot</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
      </SidebarInset>
    </SidebarProvider>
  );
}

// export async function getStaticProps() {
//   const { data, error } = await supabase
//     .from("behavioural_skills")
//     .select(
//       "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name)"
//     );

//   if (data) {
//     console.log(data);
//   }

//   if (error) {
//     console.log(error);
//   }

//   const behavioural_skills = data;

//   return {
//     props: { behavioural_skills },
//   };
// }

export const getStaticProps = async () => {
  const bdata = await supabase
    .from("behavioural_skills")
    .select(
      "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name)"
    );

  const techdata = await supabase
    .from("technical_skills")
    .select("id, skill_name, technical_sub_skills (id, sub_skill_name)");

  const techdataUser = await supabase
    .from("technical_skills")
    .select("id, skill_name, role_mapping (id, role_id)");

  const responses = await Promise.all([bdata, techdata, techdataUser]);

  // console.log(responses);

  return {
    props: {
      behavioural_skills: responses[0].data,
      technical_skills: responses[1].data,
      tech_data_user: responses[2].data,
      // tech_data_user: techdataUser.data,
    },
  };
};
